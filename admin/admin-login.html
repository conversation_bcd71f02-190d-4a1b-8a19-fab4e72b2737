<!DOCTYPE html>
<html>
<head>
  <title>Ad<PERSON></title>
  <style>
    body { font-family: sans-serif; padding: 40px; max-width: 400px; margin: auto; }
    input, button { display: block; width: 100%; margin: 10px 0; padding: 10px; }
  </style>
</head>
<body>
  <h2>Admin Login</h2>
  <form id="login-form">
    <input type="text" placeholder="Username" id="username" required />
    <input type="password" placeholder="Password" id="password" required />
    <button type="submit">Login</button>
  </form>

  <script>
    document.getElementById('login-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      const res = await fetch('http://localhost:3001/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });

      const data = await res.json();

      if (res.ok) {
        localStorage.setItem('token', data.token);
        window.location.href = 'admin-dashboard.html';
      } else {
        alert('Login failed');
      }
    });
  </script>
</body>
</html>