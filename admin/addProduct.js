// addProduct.js
import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js/+esm';

// 🔐 Replace these with YOUR values from Supabase → Settings → API
const supabase = createClient(
  'https://acpkabyizjbecftqejog.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFjcGthYnlpempiZWNmdHFlam9nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MTQxNzQsImV4cCI6MjA2NjE5MDE3NH0.JQp5PH368DLfb80nPSztzASsyom-nq6O_37ChjfvLfk'
);

document.getElementById('product-form').addEventListener('submit', async (e) => {
  e.preventDefault();

  const name = document.getElementById('name').value;
  const description = document.getElementById('description').value;
  const price = parseFloat(document.getElementById('price').value);
  const quantity = parseInt(document.getElementById('quantity').value);
  const category = document.getElementById('category').value.toLowerCase();
  const imageFile = document.getElementById('image').files[0];

  if (!imageFile) {
    alert("Please select an image.");
    return;
  }

  // 1. Upload to Supabase Storage
  const filename = `${Date.now()}-${imageFile.name}`;
  const { data: storageData, error: uploadError } = await supabase
    .storage
    .from('product-images')
    .upload(filename, imageFile);

  if (uploadError) {
    console.error(uploadError);
    alert("Image upload failed.");
    return;
  }

  const image_url = supabase
    .storage
    .from('product-images')
    .getPublicUrl(filename).data.publicUrl;

  // 2. Insert product into Supabase table
  const { error: insertError } = await supabase
    .from('products')
    .insert([{
      name,
      description,
      price,
      quantity,
      category,
      image_url
    }]);

  if (insertError) {
    console.error(insertError);
    alert("Failed to add product.");
  } else {
    alert("✅ Product added successfully!");
    document.getElementById('product-form').reset();
  }
});