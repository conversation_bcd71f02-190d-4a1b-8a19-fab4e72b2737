// Firebase configuration module
const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin SDK
const initializeFirebase = () => {
  if (!admin.apps.length) {
    try {
      // Try to use service account file first
      const serviceAccount = require('./firebase-service-account.json');
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET
      });
    } catch (error) {
      // Fallback to environment variables (for production)
      admin.initializeApp({
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET
      });
    }
  }
  return admin;
};

// Get Firestore database instance
const getFirestore = () => {
  const firebaseAdmin = initializeFirebase();
  return firebaseAdmin.firestore();
};

// Get Firebase Storage instance
const getStorage = () => {
  const firebaseAdmin = initializeFirebase();
  return firebaseAdmin.storage();
};

module.exports = {
  initializeFirebase,
  getFirestore,
  getStorage,
  admin
};
